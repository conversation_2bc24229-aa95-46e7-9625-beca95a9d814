const path = require('path');
const chalk = require('chalk');

/**
 * BuildErrorAnalyzer - 构建错误分析器（重构版）
 *
 * 专门负责：
 * - 构建错误输出分析
 * - 错误类型识别和分类
 * - 文件路径有效性验证
 * - 错误哈希生成和去重
 * - 与PromptBuilder协作生成分析提示词
 * - AI分析响应解析
 */
class BuildErrorAnalyzer {
  constructor(projectPath, aiService, toolExecutor, options = {}) {
    this.projectPath = projectPath;
    this.aiService = aiService;
    this.toolExecutor = toolExecutor;
    this.options = {
      verbose: false,
      ...options
    };

    this.promptBuilder = null;
  }

  /**
   * 设置PromptBuilder实例
   */
  setPromptBuilder(promptBuilder) {
    this.promptBuilder = promptBuilder;
  }

  /**
   * 分析构建错误并选择需要修复的文件
   */
  async analyzeBuildErrors(buildOutput, attemptNumber = 1) {
    if (!this.aiService.isEnabled()) {
      throw new Error('AI 服务不可用，无法进行错误分析');
    }

    try {
      if (this.options.verbose) {
        console.log(chalk.gray(`🔍 构建错误输出长度: ${buildOutput.length} 字符`));
      }

      // 第一步：去重错误信息
      const deduplicatedOutput = this.deduplicateErrors(buildOutput);

      // 第二步：生成常见问题建议
      const suggestions = this.generateErrorSuggestions(deduplicatedOutput);

      const context = {
        attemptNumber,
        taskType: 'error-analysis',
        buildOutputLength: deduplicatedOutput.length,
        originalLength: buildOutput.length,
        suggestions
      };

      // 如果没有PromptBuilder，使用传统方式生成提示词
      let prompt;
      if (this.promptBuilder) {
        prompt = this.promptBuilder.buildErrorAnalysisPrompt(deduplicatedOutput, context);
      } else {
        prompt = this.generateAnalysisPrompt(deduplicatedOutput, suggestions);
      }

      const response = await this.aiService.callAI(prompt, {
        context: {
          taskType: 'error-analysis',
          attemptNumber: attemptNumber,
          phase: 'analysis',
          buildOutputLength: deduplicatedOutput.length,
          suggestionsCount: suggestions.length
        }
      });

      if (this.options.verbose) {
        console.log(chalk.gray(`🤖 AI 响应长度: ${response.length} 字符`));
      }

      const filesToFix = this.parseAnalysisResponse(response);

      if (filesToFix.length === 0) {
        throw new Error('AI 未能识别需要修复的文件');
      }

      console.log(chalk.gray(`✅ AI 识别出 ${filesToFix.length} 个需要修复的文件`));
      filesToFix.forEach(file => {
        console.log(chalk.gray(`  - ${file}`));
      });

      return {
        success: true,
        filesToFix,
        suggestions,
        processedOutput: deduplicatedOutput
      };
    } catch (error) {
      console.log(chalk.red(`❌ AI 分析异常: ${error.message}`));
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 生成错误分析提示词（备用方法）
   */
  generateAnalysisPrompt(buildOutput, suggestions = []) {
    const maxOutputLength = 5000;
    const truncatedOutput = buildOutput.length > maxOutputLength
      ? buildOutput.substring(0, maxOutputLength) + '\n... (输出已截断)'
      : buildOutput;

    // 添加建议到提示词中
    const suggestionsText = this.formatSuggestionsForPrompt(suggestions);

    return `你是一个专业的 Vue 2 到 Vue 3 迁移专家和构建错误分析师。请分析以下构建错误输出，并确定需要修复的文件。

**任务目标**：
1. 分析构建错误输出
2. 识别导致错误的具体文件
3. 返回需要修复的文件路径列表

**构建错误输出**：
\`\`\`
${truncatedOutput}
\`\`\`
${suggestionsText}

**工具可用**：
你可以使用以下工具来帮助分析：
${this.toolExecutor.getToolsDescription()}

**响应格式**：
请使用以下 XML 格式返回分析结果：

\`\`\`xml
<analysis>
<files_to_fix>
<file>src/components/Example.vue</file>
<file>src/utils/helper.js</file>
</files_to_fix>
<reasoning>
简要说明为什么选择这些文件进行修复
</reasoning>
</analysis>
\`\`\`

请仔细分析错误信息，重点关注：
- 文件路径和行号信息
- 模块导入错误
- Vue 2/3 兼容性问题
- TypeScript 类型错误
- 依赖包问题
- Webpack 配置错误

**重要约束**：
- 只返回项目源代码文件，不要包含 node_modules 中的文件
- 对于 Webpack 配置错误，应该检查 vue.config.js、webpack.config.js 等配置文件
- 对于插件错误，重点关注项目配置而非第三方库内部文件
- 文件路径应该相对于项目根目录
- 参考上面的修复建议，优先处理有明确解决方案的错误

只返回确实需要修改代码的文件，不要包含 node_modules 或系统文件。`;
  }

  /**
   * 解析 AI 分析响应
   */
  parseAnalysisResponse(response) {
    try {
      const xmlMatch = response.match(/<analysis>[\s\S]*?<files_to_fix>([\s\S]*?)<\/files_to_fix>[\s\S]*?<\/analysis>/);

      if (xmlMatch) {
        const filesSection = xmlMatch[1];
        const fileMatches = filesSection.match(/<file>(.*?)<\/file>/g);

        if (fileMatches) {
          return fileMatches.map(match => {
            let file = match.replace(/<\/?file>/g, '').trim();
            if (file.startsWith(this.projectPath)) {
              file = path.relative(this.projectPath, file);
            }
            return file;
          }).filter(file => this.isValidProjectFile(file));
        }
      }

      const lines = response.split('\n');
      const files = [];

      for (const line of lines) {
        const fileMatch = line.match(/(?:src\/|\.\/)?[\w/\-.]+\.(vue|js|ts|jsx|tsx)$/);
        if (fileMatch) {
          files.push(fileMatch[0]);
        }
      }

      return [...new Set(files)].filter(file => this.isValidProjectFile(file));
    } catch (error) {
      console.warn(chalk.yellow('⚠️  解析 AI 分析响应失败，使用空列表'));
      return [];
    }
  }

  /**
   * 验证文件是否为有效的项目文件
   */
  isValidProjectFile(filePath) {
    if (!filePath || typeof filePath !== 'string') {
      return false;
    }

    if (filePath.includes('node_modules')) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`  ⚠️  跳过 node_modules 文件: ${filePath}`));
      }
      return false;
    }

    if (path.isAbsolute(filePath) && !filePath.startsWith(this.projectPath)) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`  ⚠️  跳过系统文件: ${filePath}`));
      }
      return false;
    }

    const allowedExtensions = ['.vue', '.js', '.ts', '.jsx', '.tsx', '.json', '.scss', '.css'];
    const allowedConfigFiles = ['vue.config.js', 'webpack.config.js', 'vite.config.js', 'vite.config.ts'];

    const ext = path.extname(filePath);
    const fileName = path.basename(filePath);

    if (allowedExtensions.includes(ext) || allowedConfigFiles.includes(fileName)) {
      return true;
    }

    if (this.options.verbose) {
      console.log(chalk.yellow(`  ⚠️  跳过不支持的文件类型: ${filePath}`));
    }
    return false;
  }

  /**
   * 去除重复的构建错误信息
   */
  deduplicateErrors(buildOutput) {
    if (!buildOutput || typeof buildOutput !== 'string') {
      return buildOutput;
    }

    const lines = buildOutput.split('\n');
    const seenErrors = new Set();
    const deduplicatedLines = [];

    for (const line of lines) {
      // 标准化错误行，去除时间戳、路径等变化部分
      const normalizedLine = this.normalizeErrorLine(line);

      // 如果是错误/警告行且已经见过，跳过
      if (this.isErrorOrWarningLine(line) && seenErrors.has(normalizedLine)) {
        continue;
      }

      // 记录错误行
      if (this.isErrorOrWarningLine(line)) {
        seenErrors.add(normalizedLine);
      }

      deduplicatedLines.push(line);
    }

    const originalLength = lines.length;
    const deduplicatedLength = deduplicatedLines.length;

    if (this.options.verbose && originalLength !== deduplicatedLength) {
      console.log(chalk.gray(`🔄 错误去重: ${originalLength} → ${deduplicatedLength} 行 (减少 ${originalLength - deduplicatedLength} 行重复)`));
    }

    return deduplicatedLines.join('\n');
  }

  /**
   * 判断是否为错误或警告行
   */
  isErrorOrWarningLine(line) {
    const lowerLine = line.toLowerCase();
    return lowerLine.includes('error') ||
           lowerLine.includes('warning') ||
           lowerLine.includes('failed') ||
           lowerLine.includes('validationerror') ||
           lowerLine.includes('syntaxerror') ||
           lowerLine.includes('referenceerror') ||
           lowerLine.includes('[@vue/compiler-sfc]') ||
           lowerLine.includes('module not found') ||
           lowerLine.includes('syntax error');
  }

  /**
   * 标准化错误行，去除变化的部分
   */
  normalizeErrorLine(line) {
    return line
      // 去除时间戳
      .replace(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/g, '[TIMESTAMP]')
      // 去除具体的文件路径，保留相对路径结构
      .replace(/\/[^\s]+\/([^\/\s]+\.(vue|js|ts|jsx|tsx|scss|css))/g, '[PATH]/$1')
      // 去除行号和列号
      .replace(/:\d+:\d+/g, ':LINE:COL')
      // 去除 webpack 构建进度符号
      .replace(/[⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏]/g, '')
      // 标准化空白字符
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * 生成构建错误的常见问题建议
   */
  generateErrorSuggestions(buildOutput) {
    const suggestions = [];
    const lines = buildOutput.split('\n');

    for (const line of lines) {
      const lowerLine = line.toLowerCase();

      // SCSS 变量未定义错误
      if (lowerLine.includes('syntax error: undefined variable') ||
          lowerLine.includes('undefined variable')) {
        const variableMatch = line.match(/\$[\w-]+/);
        if (variableMatch) {
          suggestions.push({
            type: 'scss_undefined_variable',
            error: line.trim(),
            suggestion: `SCSS 变量 ${variableMatch[0]} 未定义。建议：
1. 检查是否有 variables.scss 或类似的变量文件
2. 使用 list_files 工具搜索项目中的 SCSS 变量文件
3. 确保变量文件已正确导入到当前文件中
4. 检查变量名拼写是否正确`,
            tools: ['list_files', 'read_file'],
            searchPattern: '**/*variable*.scss'
          });
        }
      }

      // 模块未找到错误
      if (lowerLine.includes('module not found') || lowerLine.includes("can't resolve")) {
        const moduleMatch = line.match(/'([^']+)'/) || line.match(/"([^"]+)"/);
        if (moduleMatch) {
          const moduleName = moduleMatch[1];
          suggestions.push({
            type: 'module_not_found',
            error: line.trim(),
            module: moduleName,
            suggestion: `模块 '${moduleName}' 未找到。建议：
1. 检查 ComponentSearcher.loadComponentMappings 是否有对应的解决方案
2. 确认模块是否已安装在 package.json 中
3. 检查导入路径是否正确
4. 对于第三方组件，查看是否需要 Vue 3 兼容版本`,
            tools: ['read_file', 'list_files'],
            checkComponentMappings: true
          });
        }
      }

      // Vue 深度选择器警告
      if (lowerLine.includes('::v-deep usage as a combinator has been deprecated')) {
        suggestions.push({
          type: 'vue_deep_selector',
          error: line.trim(),
          suggestion: `Vue 3 深度选择器语法已更改。建议：
1. 将 ::v-deep <selector> 替换为 :deep(<selector>)
2. 这是 Vue 3 的新语法要求
3. 可以批量替换项目中所有的 ::v-deep 用法`,
          tools: ['str_replace'],
          replacement: {
            from: '::v-deep ',
            to: ':deep('
          }
        });
      }

      // TypeScript 类型错误
      if (lowerLine.includes('ts(') || lowerLine.includes('typescript error')) {
        suggestions.push({
          type: 'typescript_error',
          error: line.trim(),
          suggestion: `TypeScript 类型错误。建议：
1. 检查类型定义是否正确
2. 确认导入的类型是否存在
3. 检查 Vue 3 相关的类型定义是否已更新
4. 考虑添加类型断言或更新类型定义`,
          tools: ['read_file', 'str_replace']
        });
      }

      // Webpack 配置错误
      if (lowerLine.includes('configuration') && (lowerLine.includes('error') || lowerLine.includes('invalid'))) {
        suggestions.push({
          type: 'webpack_config_error',
          error: line.trim(),
          suggestion: `Webpack 配置错误。建议：
1. 检查 vue.config.js 或 webpack.config.js 配置
2. 确认 Vue 3 相关的 loader 配置是否正确
3. 检查插件版本是否与 Vue 3 兼容
4. 参考 Vue 3 官方迁移指南更新配置`,
          tools: ['read_file', 'list_files'],
          configFiles: ['vue.config.js', 'webpack.config.js', 'vite.config.js']
        });
      }
    }

    // 去重建议
    const uniqueSuggestions = this.deduplicateSuggestions(suggestions);

    if (uniqueSuggestions.length > 0 && this.options.verbose) {
      console.log(chalk.gray(`💡 生成了 ${uniqueSuggestions.length} 个错误修复建议`));
    }

    return uniqueSuggestions;
  }

  /**
   * 去重建议
   */
  deduplicateSuggestions(suggestions) {
    const seen = new Set();
    return suggestions.filter(suggestion => {
      const key = `${suggestion.type}:${suggestion.error}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  /**
   * 格式化建议为提示词文本
   */
  formatSuggestionsForPrompt(suggestions) {
    if (!suggestions || suggestions.length === 0) {
      return '';
    }

    let promptText = '\n\n**🔧 常见错误修复建议**：\n';

    suggestions.forEach((suggestion, index) => {
      promptText += `\n${index + 1}. **${suggestion.type.replace(/_/g, ' ').toUpperCase()}**\n`;
      promptText += `   错误: ${suggestion.error}\n`;
      promptText += `   建议: ${suggestion.suggestion}\n`;

      if (suggestion.tools) {
        promptText += `   推荐工具: ${suggestion.tools.join(', ')}\n`;
      }

      if (suggestion.searchPattern) {
        promptText += `   搜索模式: ${suggestion.searchPattern}\n`;
      }

      if (suggestion.configFiles) {
        promptText += `   相关配置文件: ${suggestion.configFiles.join(', ')}\n`;
      }
    });

    return promptText;
  }

  /**
   * 生成错误哈希
   */
  generateErrorHash(buildOutput) {
    const errorLines = buildOutput.split('\n').filter(line => {
      const lowerLine = line.toLowerCase();
      return lowerLine.includes('error') ||
             lowerLine.includes('failed') ||
             lowerLine.includes('validationerror') ||
             lowerLine.includes('syntaxerror') ||
             lowerLine.includes('referenceerror');
    });

    const normalizedErrors = errorLines.map(line => {
      return line
        .replace(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/g, '[TIMESTAMP]')
        .replace(/\/[^\s]+\//g, '[PATH]/')
        .replace(/\s+/g, ' ')
        .trim();
    });

    return normalizedErrors.join('|');
  }
}

module.exports = BuildErrorAnalyzer;
