const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const puppeteer = require('puppeteer');
const { spawn, exec } = require('child_process');
const RuntimeErrorHandler = require('../runtime-inject/RuntimeErrorHandler');
const BrowserDetector = require('./BrowserDetector');
const BuildFixAgent = require('../build-fix/ai/BuildFixAgent');
const BuildErrorAnalyzer = require('../build-fix/ai/BuildErrorAnalyzer');
const AutoLoginManager = require('./AutoLoginManager');
const FAQHelper = require('./FAQHelper');
const ErrorAnalyzer = require('./ErrorAnalyzer');

/**
 * PageValidator - 页面运行时验证器
 *
 * 功能：
 * 1. 启动开发服务器
 * 2. 使用 Puppeteer 访问每个路由页面
 * 3. 捕获页面错误和控制台输出
 * 4. 集成 RuntimeErrorHandler 进行错误处理
 * 5. 生成验证报告
 */
class PageValidator {
  constructor(projectPath, routes, options = {}) {
    this.projectPath = projectPath;
    this.allRoutes = routes || [];
    this.routeParser = options.routeParser || null; // 路由解析器实例

    // 过滤路由（如果指定了特定路由）
    if (options.specificRoutes && options.specificRoutes.length > 0) {
      this.routes = this.allRoutes.filter(route =>
        options.specificRoutes.includes(route.path)
      );
      console.log(chalk.yellow(`🎯 只验证指定的 ${this.routes.length} 个路由: ${options.specificRoutes.join(', ')}`));
    } else {
      this.routes = this.allRoutes;
    }
    this.options = {
      port: 3000,
      timeout: 30000,
      headless: 'new', // 使用新的 headless 模式
      devCommand: 'npm run dev',
      baseUrl: null,
      verbose: false,
      autoFix: false,
      maxFixAttempts: 3, // 最大修复尝试次数
      revalidateAfterFix: true, // 修复后重新验证页面
      dryRun: false, // 预览模式，不实际修改文件
      waitForServer: 60000,  // 等待服务器启动时间
      pageTimeout: 15000,   // 增加页面超时时间到15秒
      navigationTimeout: 20000, // 导航超时时间
      executablePath: null, // 浏览器可执行文件路径
      routerMode: 'hash', // Vue Router模式: 'hash' 或 'history'
      loginCredentials: {   // 登录凭据
        username: 'admin',
        password: '111111'
      },
      skipLogin: false,     // 是否跳过自动登录
      ...options
    };

    this.baseUrl = this.options.baseUrl || `http://localhost:${this.options.port}`;
    this.devServer = null;
    this.browser = null;
    this.validationResults = [];
    this.errors = [];
    this.isLoggedIn = false; // 登录状态标记

    // 浏览器检测器
    this.browserDetector = new BrowserDetector({
      verbose: this.options.verbose,
      preferredBrowsers: ['chrome', 'chromium', 'edge']
    });

    // 集成运行时错误处理器
    if (this.options.autoFix) {
      this.runtimeErrorHandler = new RuntimeErrorHandler(projectPath, {
        port: this.options.port,
        autoFix: true,
        verbose: this.options.verbose
      });
    }

    // 集成 BuildFixAgent 用于页面错误修复
    if (this.options.autoFix) {
      this.buildFixAgent = new BuildFixAgent(projectPath, {
        maxAttempts: this.options.maxFixAttempts || 3,
        verbose: this.options.verbose,
        dryRun: this.options.dryRun || false
      });
    }

    // 初始化自动登录管理器
    this.autoLoginManager = new AutoLoginManager({
      username: this.options.loginCredentials?.username || this.options.username || 'admin',
      password: this.options.loginCredentials?.password || this.options.password || '111111',
      verbose: this.options.verbose,
      aiEnabled: !this.options.skipLogin,
      configPath: path.join(projectPath, '.login-config.json')
    });

    // 初始化 FAQ 助手
    this.faqHelper = new FAQHelper(projectPath, {
      verbose: this.options.verbose
    });

    // 初始化错误分析器
    this.errorAnalyzer = new ErrorAnalyzer({
      verbose: this.options.verbose
    });

    // 初始化构建错误分析器（用于统一的错误输出处理）
    this.buildErrorAnalyzer = new BuildErrorAnalyzer(projectPath, null, null, {
      verbose: this.options.verbose
    });
  }

  /**
   * 验证所有页面
   */
  async validateAllPages() {
    console.log(chalk.blue(`🔍 开始验证 ${this.routes.length} 个页面...`));

    if (this.options.verbose) {
      console.log(chalk.gray(`   详细模式已启用`));
      console.log(chalk.gray(`   页面超时: ${this.options.pageTimeout}ms`));
      console.log(chalk.gray(`   导航超时: ${this.options.navigationTimeout}ms`));
      console.log(chalk.gray(`   自动修复: ${this.options.autoFix ? '启用' : '禁用'}`));
      console.log(chalk.gray(`   路由模式: ${this.options.routerMode}`));
    }

    try {
      // 0. 初始化 FAQ 系统
      await this.faqHelper.initialize();

      // 1. 启动开发服务器
      await this.startDevServer();

      // 2. 启动浏览器
      await this.startBrowser();

      // 3. 验证每个页面
      for (let i = 0; i < this.routes.length; i++) {
        const route = this.routes[i];
        console.log(chalk.gray(`   [${i + 1}/${this.routes.length}] 验证页面: ${route.path}`));

        const result = await this.validateSinglePage(route);
        this.validationResults.push(result);

        // 显示简化的结果
        if (result.success) {
          console.log(chalk.green(`   ✅ 成功`));
          if (this.options.verbose) {
            console.log(chalk.gray(`      加载时间: ${result.loadTime}ms`));
            if (result.warnings.length > 0) {
              console.log(chalk.yellow(`      警告: ${result.warnings.length} 个`));
            }
          }
        } else {
          console.log(chalk.red(`   ❌ 失败: ${result.errors.length} 个错误`));

          // 在verbose模式下显示更多信息
          if (this.options.verbose) {
            console.log(chalk.gray(`      加载时间: ${result.loadTime}ms`));
            console.log(chalk.gray(`      URL: ${result.url}`));
            if (result.warnings.length > 0) {
              console.log(chalk.yellow(`      警告: ${result.warnings.length} 个`));
            }
            if (result.needsLogin) {
              console.log(chalk.yellow(`      需要登录: ${result.loginAttempted ? '已尝试' : '未尝试'}`));
            }
          }

          // 显示错误摘要（如果之前没有显示过且不是自动修复模式）
          if (!this.options.autoFix || !this.buildFixAgent) {
            this.errorAnalyzer.displayErrorSummary(result.errors, 2);
          }

          if (result.fixAttempted) {
            const fixStatus = result.fixResult?.success ? '成功' : '失败';
            console.log(chalk.yellow(`   🔧 已尝试自动修复: ${fixStatus}`));

            if (this.options.verbose && result.fixResult) {
              if (result.fixResult.success) {
                console.log(chalk.gray(`      修复文件: ${result.fixResult.filesModified || 0} 个`));
              } else {
                console.log(chalk.gray(`      修复失败原因: ${result.fixResult.error || '未知'}`));
              }
            }
          }
        }

        // 短暂延迟避免过快访问 - 进一步减少延迟时间
        await this.sleep(100);
      }

      // 4. 生成报告
      const report = this.generateReport();

      console.log(chalk.green(`✅ 页面验证完成`));
      this.printSummary();

      return {
        success: true,
        results: this.validationResults,
        report: report,
        errors: this.errors
      };

    } catch (error) {
      console.error(chalk.red(`❌ 页面验证失败: ${error.message}`));
      return {
        success: false,
        results: this.validationResults,
        report: null,
        errors: [...this.errors, error.message]
      };
    } finally {
      await this.cleanup();
    }
  }

  /**
   * 启动开发服务器
   */
  async startDevServer() {
    if (this.options.baseUrl) {
      console.log(chalk.gray(`   使用外部服务器: ${this.baseUrl}`));
      return;
    }

    console.log(chalk.gray(`   启动开发服务器...`));

    return new Promise((resolve, reject) => {
      // 解析开发命令
      const [command, ...args] = this.options.devCommand.split(' ');

      this.devServer = spawn(command, args, {
        cwd: this.projectPath,
        stdio: 'pipe', // 总是使用 pipe 以便监听输出
        env: {
          ...process.env,
          PORT: this.options.port.toString()
        }
      });

      let serverReady = false;
      let output = '';
      let detectedPort = null;

      // 监听输出判断服务器是否启动
      if (this.devServer.stdout) {
        this.devServer.stdout.on('data', (data) => {
          const text = data.toString();
          output += text;

          if (this.options.verbose) {
            console.log(text);
          }

          // 添加更多调试信息
          // if (this.options.verbose && !serverReady) {
          //   console.log(chalk.cyan(`   🔍 检查文本: "${text.trim()}"`));
          //   console.log(chalk.cyan(`   🔍 包含 'Local:': ${text.includes('Local:')}`));
          //   console.log(chalk.cyan(`   🔍 包含 'localhost:': ${text.includes('localhost:')}`));
          //   console.log(chalk.cyan(`   🔍 包含 'App running at': ${text.includes('App running at')}`));
          // }

          // 检查服务器启动标志并提取端口
          if (!serverReady && (
            text.includes('Local:') ||
            text.includes('localhost:') ||
            text.includes('App running at') ||
            text.includes('Network:')
          )) {
            if (this.options.verbose) {
              console.log(chalk.blue(`   🔍 检测到服务器启动信息: ${text.trim()}`));
            }

            // 尝试提取端口号
            const portMatch = text.match(/localhost:(\d+)/);
            if (portMatch) {
              detectedPort = parseInt(portMatch[1]);
              if (this.options.verbose) {
                console.log(chalk.gray(`   检测到服务器端口: ${detectedPort}`));
              }

              // 验证服务器是否真的可以访问
              this.verifyServerWithRetry(detectedPort, resolve, reject);
            }
          }
        });
      }

      if (this.devServer.stderr) {
        this.devServer.stderr.on('data', (data) => {
          const text = data.toString();
          if (this.options.verbose) {
            console.error(chalk.red(text));
          }
        });
      }

      this.devServer.on('error', (error) => {
        reject(new Error(`启动开发服务器失败: ${error.message}`));
      });

      this.devServer.on('exit', (code) => {
        if (code !== 0 && !serverReady) {
          reject(new Error(`开发服务器异常退出，代码: ${code}`));
        }
      });

      // 超时处理
      setTimeout(() => {
        if (!serverReady) {
          reject(new Error(`开发服务器启动超时 (${this.options.waitForServer}ms)`));
        }
      }, this.options.waitForServer);
    });
  }

  /**
   * 带重试的服务器验证
   */
  async verifyServerWithRetry(port, resolve, reject, attempt = 1, maxAttempts = 5) {
    const delay = attempt * 2000; // 递增延迟：2s, 4s, 6s, 8s, 10s

    setTimeout(async () => {
      try {
        const isReady = await this.verifyServerReady(port);

        if (isReady) {
          this.options.port = port; // 更新实际端口
          this.baseUrl = `http://localhost:${port}`;
          if (this.options.verbose) {
            console.log(chalk.green(`   ✅ 服务器验证成功 (端口: ${port}, 尝试: ${attempt}/${maxAttempts})`));
          }
          resolve();
        } else if (attempt < maxAttempts) {
          if (this.options.verbose) {
            console.log(chalk.yellow(`   ⚠️  端口 ${port} 验证失败，${delay/1000}秒后重试 (${attempt}/${maxAttempts})...`));
          }
          this.verifyServerWithRetry(port, resolve, reject, attempt + 1, maxAttempts);
        } else {
          if (this.options.verbose) {
            console.log(chalk.red(`   ❌ 服务器验证失败，已达到最大重试次数 (${maxAttempts})`));
          }
          // 最后一次尝试失败，但不要 reject，让超时处理
        }
      } catch (error) {
        if (this.options.verbose) {
          console.log(chalk.yellow(`   ⚠️  服务器验证出错 (尝试 ${attempt}/${maxAttempts}): ${error.message}`));
        }
        if (attempt < maxAttempts) {
          this.verifyServerWithRetry(port, resolve, reject, attempt + 1, maxAttempts);
        }
      }
    }, delay);
  }

  /**
   * 验证服务器是否真的可以访问
   */
  async verifyServerReady(port) {
    try {
      const axios = require('axios');
      const response = await axios.get(`http://localhost:${port}`, {
        timeout: 5000,
        validateStatus: () => true // 接受任何状态码
      });

      return response.status < 500; // 只要不是服务器错误就认为可用
    } catch (error) {
      return false;
    }
  }

  /**
   * 确保浏览器可用
   */
  async ensureBrowser() {
    try {
      const selectedBrowser = await this.browserDetector.ensureBrowser();

      // 如果选择的是系统浏览器，设置可执行文件路径
      if (selectedBrowser.type !== 'puppeteer') {
        this.options.executablePath = selectedBrowser.executablePath;
      }

      return selectedBrowser;
    } catch (error) {
      throw new Error(`浏览器检测失败: ${error.message}`);
    }
  }

  /**
   * 启动浏览器
   */
  async startBrowser() {
    console.log(chalk.gray(`   启动浏览器...`));

    // 确保浏览器可用
    await this.ensureBrowser();

    const launchOptions = {
      headless: this.options.headless === 'new' ? 'new' : this.options.headless,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding'
      ]
    };

    // 如果检测到系统浏览器，使用它
    if (this.options.executablePath) {
      launchOptions.executablePath = this.options.executablePath;
    }

    try {
      this.browser = await puppeteer.launch(launchOptions);
      console.log(chalk.green('✅ 浏览器启动成功'));
    } catch (error) {
      // 如果启动失败，尝试使用新的 headless 模式
      if (!launchOptions.headless || launchOptions.headless === true) {
        console.log(chalk.yellow('⚠️  尝试使用新的 headless 模式...'));
        launchOptions.headless = 'new';
        this.browser = await puppeteer.launch(launchOptions);
        console.log(chalk.green('✅ 浏览器启动成功 (新 headless 模式)'));
      } else {
        throw error;
      }
    }
  }

  /**
   * 构建页面URL
   */
  buildPageUrl(route) {
    if (this.options.routerMode === 'hash') {
      // Hash模式: http://localhost:9527/#/example/list
      return `${this.baseUrl}/#${route.path}`;
    } else {
      // History模式: http://localhost:9527/example/list
      return `${this.baseUrl}${route.path}`;
    }
  }

  /**
   * 验证单个页面
   */
  async validateSinglePage(route) {
    const url = this.buildPageUrl(route);
    const result = {
      route: route,
      url: url,
      success: false,
      errors: [],
      warnings: [],
      consoleMessages: [],
      networkErrors: [],
      loadTime: 0,
      timestamp: new Date().toISOString(),
      needsLogin: false,
      loginAttempted: false
    };

    try {
      const page = await this.browser.newPage();
      const startTime = Date.now();

      // 设置页面事件监听
      this.setupPageListeners(page, result);

      // 设置超时
      page.setDefaultTimeout(this.options.pageTimeout);
      page.setDefaultNavigationTimeout(this.options.navigationTimeout);

      // 访问页面 - 使用更宽松的等待条件和重试机制
      let response;
      let navigationAttempts = 0;
      const maxNavigationAttempts = 3;

      while (navigationAttempts < maxNavigationAttempts) {
        try {
          if (this.options.verbose && navigationAttempts > 0) {
            console.log(chalk.gray(`    🔄 重试页面导航 (第 ${navigationAttempts + 1} 次尝试)...`));
          }

          response = await page.goto(url, {
            waitUntil: 'domcontentloaded', // 使用较快的等待条件
            timeout: this.options.navigationTimeout
          });
          break; // 成功则跳出循环
        } catch (navigationError) {
          navigationAttempts++;
          if (navigationAttempts >= maxNavigationAttempts) {
            throw navigationError; // 最后一次尝试失败则抛出错误
          }

          if (this.options.verbose) {
            console.log(chalk.yellow(`    ⚠️  页面导航失败: ${navigationError.message}，将重试...`));
          }

          // 等待一段时间后重试
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }

      result.loadTime = Date.now() - startTime;

      // 检查响应状态
      /// todo skip 304 in future
      // if (!response.ok()) {
      //   result.errors.push(`HTTP ${response.status()}: ${response.statusText()}`);
      // }

      // 检查是否被重定向到登录页面
      const finalUrl = page.url();
      if (finalUrl.includes('/login') && !url.includes('/login')) {
        result.needsLogin = true;

        if (this.options.verbose) {
          console.log(chalk.yellow(`    🔐 页面被重定向到登录页面: ${finalUrl}`));
        }

        // 如果还没有登录或登录失效，尝试登录
        if (!this.isLoggedIn && !this.options.skipLogin) {
          if (this.options.verbose) {
            console.log(chalk.yellow(`    🔑 尝试登录后重新访问页面...`));
          }

          result.loginAttempted = true;
          const loginSuccess = await this.autoLoginManager.attemptLogin(page);

          if (loginSuccess) {
            this.isLoggedIn = true;

            if (this.options.verbose) {
              console.log(chalk.green(`    🔄 登录成功，重新访问目标页面...`));
            }

            // 登录成功后重新访问目标页面
            await page.goto(url, {
              waitUntil: 'domcontentloaded',
              timeout: this.options.pageTimeout
            });

            const retryUrl = page.url();
            if (!retryUrl.includes('/login')) {
              if (this.options.verbose) {
                console.log(chalk.green(`    ✅ 登录后成功访问页面: ${retryUrl}`));
              }
            } else {
              result.errors.push('登录后仍被重定向到登录页面，可能是权限不足');
              this.isLoggedIn = false; // 重置登录状态
            }
          } else {
            result.errors.push('页面需要登录权限，自动登录失败');
          }
        } else if (this.isLoggedIn) {
          // 已经登录但仍被重定向，可能是登录失效或权限不足
          if (this.options.verbose) {
            console.log(chalk.yellow(`    ⚠️  已登录但被重定向，可能登录失效，重新尝试登录...`));
          }

          // 重置登录状态并重新尝试
          this.isLoggedIn = false;
          result.loginAttempted = true;
          const loginSuccess = await this.autoLoginManager.attemptLogin(page);

          if (loginSuccess) {
            this.isLoggedIn = true;
            // 重新访问目标页面
            await page.goto(url, {
              waitUntil: 'domcontentloaded',
              timeout: this.options.pageTimeout
            });

            const retryUrl = page.url();
            if (retryUrl.includes('/login')) {
              result.errors.push('页面需要特定权限，当前用户权限不足');
              this.isLoggedIn = false;
            }
          } else {
            result.errors.push('登录失效，重新登录失败');
          }
        } else {
          result.errors.push('页面需要登录权限，跳过了自动登录');
        }
      }

      // 等待页面渲染和Vue应用初始化 - 使用智能等待策略
      await this.waitForPageReady(page);

      // 检查页面是否包含 Vue 应用
      const hasVueApp = await page.evaluate(() => {
        return !!(window.Vue || document.querySelector('[data-v-]') || document.querySelector('#app'));
      });

      if (!hasVueApp) {
        result.warnings.push('页面可能未正确加载 Vue 应用');
      }

      // 截图功能 - 为每个页面截图
      try {
        const screenshotPath = await this.takeScreenshot(page, route);
        result.screenshotPath = screenshotPath;
        if (this.options.verbose) {
          console.log(chalk.gray(`    📸 页面截图已保存: ${screenshotPath}`));
        }
      } catch (screenshotError) {
        result.warnings.push(`截图失败: ${screenshotError.message}`);
        if (this.options.verbose) {
          console.log(chalk.yellow(`    ⚠️  截图失败: ${screenshotError.message}`));
        }
      }

      // 主动检测错误弹框和错误信息
      const errorDetection = await page.evaluate(() => {
        const errors = [];

        // 检查是否有包含"Uncaught runtime errors"的元素
        const allElements = document.querySelectorAll('*');
        for (const element of allElements) {
          const text = element.textContent || element.innerText || '';

          // 检查错误弹框
          if (text.includes('Uncaught runtime errors') ||
              text.includes('Cannot read properties of null') ||
              text.includes('TypeError:') ||
              text.includes('ReferenceError:') ||
              text.includes('Error:')) {
            errors.push({
              type: 'error-dialog',
              message: text.substring(0, 500),
              tagName: element.tagName,
              className: element.className,
              id: element.id
            });
          }
        }

        // 检查页面标题是否包含错误
        if (document.title.includes('Error') || document.title.includes('错误')) {
          errors.push({
            type: 'page-title-error',
            message: `页面标题包含错误: ${document.title}`
          });
        }

        // 检查控制台是否有错误（通过检查是否有错误样式的元素）
        const errorStyleElements = document.querySelectorAll([
          '.error',
          '.Error',
          '[class*="error"]',
          '[class*="Error"]',
          '.runtime-error',
          '.js-error'
        ].join(','));

        for (const element of errorStyleElements) {
          const text = element.textContent || element.innerText || '';
          if (text.length > 10) {
            errors.push({
              type: 'error-styled-element',
              message: text.substring(0, 300),
              tagName: element.tagName,
              className: element.className
            });
          }
        }

        return errors;
      });

      // 将检测到的错误添加到结果中
      for (const error of errorDetection) {
        result.errors.push({
          type: 'dom-error-detection',
          message: error.message,
          details: error
        });
      }

      // 多次检查错误，因为有些错误可能延迟出现
      let errorData = { pageErrors: [], vueErrors: [], consoleErrors: [] };

      // 第一次检查
      const firstCheck = await page.evaluate(() => {
        return {
          pageErrors: window.__pageErrors || [],
          vueErrors: window.__vueErrors || [],
          consoleErrors: window.__consoleErrors || []
        };
      });

      // 合并第一次检查的结果（安全检查）
      if (firstCheck && firstCheck.pageErrors) {
        errorData.pageErrors.push(...firstCheck.pageErrors);
      }
      if (firstCheck && firstCheck.vueErrors) {
        errorData.vueErrors.push(...firstCheck.vueErrors);
      }
      if (firstCheck && firstCheck.consoleErrors) {
        errorData.consoleErrors.push(...firstCheck.consoleErrors);
      }

      // 等待异步错误出现 - 进一步减少等待时间
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 第二次检查
      const secondCheck = await page.evaluate(() => {
        return {
          pageErrors: window.__pageErrors || [],
          vueErrors: window.__vueErrors || [],
          consoleErrors: window.__consoleErrors || []
        };
      });

      // 合并新的错误（避免重复）
      const existingErrorMessages = new Set(errorData.pageErrors.map(e => e.message));
      const existingVueMessages = new Set(errorData.vueErrors.map(e => e.message));
      const existingConsoleMessages = new Set(errorData.consoleErrors.map(e => e.message));

      // 安全检查第二次检查的结果
      if (secondCheck && secondCheck.pageErrors) {
        for (const error of secondCheck.pageErrors) {
          if (!existingErrorMessages.has(error.message)) {
            errorData.pageErrors.push(error);
          }
        }
      }

      if (secondCheck && secondCheck.vueErrors) {
        for (const error of secondCheck.vueErrors) {
          if (!existingVueMessages.has(error.message)) {
            errorData.vueErrors.push(error);
          }
        }
      }

      if (secondCheck && secondCheck.consoleErrors) {
        for (const error of secondCheck.consoleErrors) {
          if (!existingConsoleMessages.has(error.message)) {
            errorData.consoleErrors.push(error);
          }
        }
      }

      // 处理页面错误
      if (errorData.pageErrors.length > 0) {
        for (const error of errorData.pageErrors) {
          const errorMessage = error.message || 'Unknown error';
          // 只添加有效的代码错误，并避免重复
          if (this.errorAnalyzer.isValidCodeError(errorMessage) && !this.errorAnalyzer.isDuplicateError(result.errors, errorMessage)) {
            result.errors.push({
              type: error.type || 'javascript-error',
              message: errorMessage,
              details: error
            });
          }
        }
      }

      // 处理 Vue 错误
      if (errorData.vueErrors.length > 0) {
        for (const error of errorData.vueErrors) {
          const errorMessage = error.message || 'Unknown Vue error';
          if (error.type && error.type.includes('warning')) {
            result.warnings.push(`Vue Warning: ${errorMessage}`);
          } else {
            // 只添加有效的代码错误，并避免重复
            if (this.errorAnalyzer.isValidCodeError(errorMessage) && !this.errorAnalyzer.isDuplicateError(result.errors, errorMessage)) {
              result.errors.push({
                type: 'vue-error',
                message: errorMessage,
                details: error
              });
            }
          }
        }
      }

      // 处理控制台错误
      if (errorData.consoleErrors.length > 0) {
        for (const error of errorData.consoleErrors) {
          const errorMessage = error.message || 'Unknown console error';
          if (error.level === 'error') {
            // 只添加有效的代码错误，并避免重复
            if (this.errorAnalyzer.isValidCodeError(errorMessage) && !this.errorAnalyzer.isDuplicateError(result.errors, errorMessage)) {
              result.errors.push({
                type: 'console-error',
                message: errorMessage,
                details: error
              });
            }
          } else if (error.level === 'warn') {
            result.warnings.push(`Console Warning: ${errorMessage}`);
          }
        }
      }

      // 如果没有错误，标记为成功
      if (result.errors.length === 0) {
        result.success = true;
      } else if (this.options.autoFix && this.buildFixAgent) {
        // 如果启用了自动修复且有错误，尝试使用 BuildFixAgent 修复
        console.log(chalk.yellow(`  🔧 检测到 ${result.errors.length} 个错误，尝试自动修复...`));

        // 显示前几个错误信息用于调试（不管是否为详细模式）
        this.errorAnalyzer.displayErrorSummary(result.errors, 5);

        const fixResult = await this.attemptPageErrorFix(route, result.errors);

        if (fixResult.success) {
          console.log(chalk.green(`  ✅ 页面错误修复成功，修复了 ${fixResult.filesModified} 个文件`));
          result.fixAttempted = true;
          result.fixResult = fixResult;

          // 可选：重新验证页面以确认修复效果
          if (this.options.revalidateAfterFix) {
            console.log(chalk.gray(`  🔄 重新验证页面...`));

            try {
              // 等待一段时间让修复生效
              await new Promise(resolve => setTimeout(resolve, 2000));

              // 重新加载页面
              await page.reload({
                waitUntil: 'domcontentloaded',
                timeout: this.options.navigationTimeout
              });

              // 等待页面准备就绪
              await this.waitForPageReady(page);

              // 重新检查错误
              const revalidationErrors = await this.checkPageErrors(page);
              if (revalidationErrors.length < result.errors.length) {
                console.log(chalk.green(`  ✅ 修复后错误减少: ${result.errors.length} → ${revalidationErrors.length}`));
                result.errorsAfterFix = revalidationErrors;
                if (revalidationErrors.length === 0) {
                  result.success = true;
                }
              } else {
                console.log(chalk.yellow(`  ⚠️  修复后错误数量未减少: ${result.errors.length} → ${revalidationErrors.length}`));
                // 显示修复后剩余的错误
                if (revalidationErrors.length > 0) {
                  console.log(chalk.gray(`    📋 剩余错误:`));
                  this.errorAnalyzer.displayErrorSummary(revalidationErrors, 3);
                }
              }
            } catch (revalidationError) {
              console.log(chalk.yellow(`  ⚠️  重新验证失败: ${revalidationError.message}`));
            }
          }
        } else {
          // 根据失败原因显示不同的消息
          if (fixResult.filteredCount > 0) {
            console.log(chalk.yellow(`  ⚠️  ${fixResult.filteredCount} 个错误无法修复（非代码错误），${fixResult.error}`));
          } else {
            console.log(chalk.yellow(`  ⚠️  页面错误修复失败: ${fixResult.error}`));
          }
          result.fixAttempted = true;
          result.fixResult = fixResult;

          // 即使修复失败，也显示详细的错误信息
          if (this.options.verbose) {
            console.log(chalk.gray(`    🔍 修复失败详情:`));
            if (fixResult.analysisResult) {
              console.log(chalk.gray(`      - 分析结果: ${JSON.stringify(fixResult.analysisResult, null, 2).substring(0, 200)}...`));
            }
            if (fixResult.fixResult) {
              console.log(chalk.gray(`      - 修复结果: ${JSON.stringify(fixResult.fixResult, null, 2).substring(0, 200)}...`));
            }
          }
        }
      } else {
        // 如果没有启用自动修复，也显示错误摘要
        this.errorAnalyzer.displayErrorSummary(result.errors, 5);

        if (this.options.verbose) {
          console.log(chalk.gray(`    💡 提示: 使用 --auto-fix 选项可以尝试自动修复这些错误`));
        }
      }

      await page.close();

    } catch (error) {
      result.errors.push(`页面访问失败: ${error.message}`);

      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️  ${route.path}: ${error.message}`));
      }
    }

    return result;
  }

  /**
   * 智能等待页面准备就绪
   */
  async waitForPageReady(page) {
    const maxWaitTime = 5000; // 最大等待5秒
    const checkInterval = 200; // 每200ms检查一次
    let waitTime = 0;

    while (waitTime < maxWaitTime) {
      try {
        // 检查页面是否已经准备就绪
        const isReady = await page.evaluate(() => {
          // 检查DOM是否加载完成
          if (document.readyState !== 'complete') {
            return false;
          }

          // 检查是否有Vue应用
          const hasVue = !!(window.Vue || document.querySelector('[data-v-]') || document.querySelector('#app'));

          // 检查是否有明显的加载指示器
          const loadingElements = document.querySelectorAll([
            '.loading',
            '.spinner',
            '.loader',
            '[class*="loading"]',
            '[class*="spinner"]'
          ].join(','));

          const hasVisibleLoading = Array.from(loadingElements).some(el => {
            const style = window.getComputedStyle(el);
            return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
          });

          // 如果有Vue应用且没有可见的加载指示器，认为页面准备就绪
          return hasVue && !hasVisibleLoading;
        });

        if (isReady) {
          if (this.options.verbose) {
            console.log(chalk.gray(`    ✅ 页面准备就绪 (等待时间: ${waitTime}ms)`));
          }
          return;
        }

        // 等待一段时间后再次检查
        await new Promise(resolve => setTimeout(resolve, checkInterval));
        waitTime += checkInterval;

      } catch (error) {
        if (this.options.verbose) {
          console.log(chalk.yellow(`    ⚠️  页面准备检查出错: ${error.message}`));
        }
        break;
      }
    }

    // 如果超时，使用固定等待时间
    if (waitTime >= maxWaitTime) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`    ⚠️  页面准备检查超时，使用固定等待时间`));
      }
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }

  /**
   * 为页面截图
   */
  async takeScreenshot(page, route) {
    // 确保截图目录存在
    const screenshotDir = path.join(this.projectPath, 'validation-reports', 'screenshots');
    await fs.ensureDir(screenshotDir);

    // 生成截图文件名
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const routeName = route.path.replace(/[\/\?#]/g, '_').replace(/^_/, '') || 'root';
    const filename = `${routeName}_${timestamp}.png`;
    const screenshotPath = path.join(screenshotDir, filename);

    // 设置视口大小
    await page.setViewport({ width: 1280, height: 800 });

    // 截图
    await page.screenshot({
      path: screenshotPath,
      fullPage: true,
      type: 'png'
    });

    return screenshotPath;
  }

  /**
   * 设置页面事件监听器
   */
  setupPageListeners(page, result) {
    // 监听控制台消息
    page.on('console', async (msg) => {
      let messageText = msg.text();

      // 如果是 JSHandle 类型的错误，尝试获取更详细的信息
      if (messageText.includes('JSHandle@') && msg.args().length > 0) {
        try {
          const args = msg.args();
          const detailedMessages = [];
          let fullStackTrace = null;

          for (const arg of args) {
            try {
              // 首先尝试获取完整的错误对象信息，包括堆栈跟踪
              const errorInfo = await arg.evaluate(obj => {
                if (obj instanceof Error) {
                  return {
                    message: obj.message,
                    stack: obj.stack,
                    name: obj.name,
                    fileName: obj.fileName,
                    lineNumber: obj.lineNumber,
                    columnNumber: obj.columnNumber
                  };
                } else if (obj && typeof obj === 'object' && obj.stack) {
                  // 处理类似错误对象的结构
                  return {
                    message: obj.message || obj.toString(),
                    stack: obj.stack,
                    name: obj.name || 'Error'
                  };
                } else if (obj && typeof obj === 'object') {
                  return {
                    message: obj.message || JSON.stringify(obj),
                    toString: obj.toString()
                  };
                } else {
                  return {
                    message: String(obj)
                  };
                }
              });

              if (errorInfo.stack) {
                fullStackTrace = errorInfo.stack;
                // 从完整堆栈中提取第一行作为主要错误信息
                const firstLine = errorInfo.stack.split('\n')[0];
                detailedMessages.push(firstLine);
              } else if (errorInfo.message) {
                detailedMessages.push(errorInfo.message);
              }

            } catch (evaluateError) {
              // 如果evaluate失败，回退到原有逻辑
              try {
                const jsonValue = await arg.jsonValue();
                if (jsonValue && typeof jsonValue === 'object') {
                  if (jsonValue.message) {
                    detailedMessages.push(jsonValue.message);
                  } else if (jsonValue.stack) {
                    fullStackTrace = jsonValue.stack;
                    detailedMessages.push(jsonValue.stack.split('\n')[0]);
                  } else {
                    detailedMessages.push(JSON.stringify(jsonValue));
                  }
                } else {
                  detailedMessages.push(String(jsonValue));
                }
              } catch (e) {
                // 如果无法获取 JSON 值，尝试获取字符串表示
                try {
                  const stringValue = await arg.evaluate(obj => {
                    // 尝试获取堆栈信息
                    if (obj && obj.stack) {
                      return obj.stack;
                    }
                    return obj.toString();
                  });

                  // 检查字符串是否包含堆栈信息
                  if (stringValue.includes('\n    at ')) {
                    fullStackTrace = stringValue;
                    detailedMessages.push(stringValue.split('\n')[0]);
                  } else {
                    detailedMessages.push(stringValue);
                  }
                } catch (e2) {
                  detailedMessages.push(messageText);
                }
              }
            }
          }

          if (detailedMessages.length > 0 && detailedMessages[0] !== messageText) {
            messageText = detailedMessages.join(' | ');
          }

          // 如果获取到完整堆栈，将其添加到消息中
          if (fullStackTrace && !messageText.includes(fullStackTrace)) {
            messageText += '\n' + fullStackTrace;
          }

        } catch (e) {
          // 如果获取详细信息失败，保持原始消息
        }
      }

      const message = {
        type: msg.type(),
        text: messageText,
        timestamp: new Date().toISOString()
      };

      result.consoleMessages.push(message);

      // 只处理 Error 级别的错误，过滤掉 Warning 和无意义的错误
      if (msg.type() === 'error') {
        // 过滤掉无意义的错误信息
        if (this.errorAnalyzer.isValidCodeError(messageText)) {
          result.errors.push(`Console Error: ${messageText}`);
        }
      } else if (msg.type() === 'warning') {
        // 只收集警告，不当作错误处理
        result.warnings.push(`Console Warning: ${messageText}`);
      }
    });

    // 监听页面错误
    page.on('pageerror', (error) => {
      let errorMessage = error.message;

      // 尝试获取完整的错误信息，包括堆栈跟踪
      if (error.stack) {
        errorMessage = error.stack;
      } else if (error.toString && error.toString() !== error.message) {
        errorMessage = error.toString();
      }

      // 过滤掉重复的页面错误
      if (this.errorAnalyzer.isValidCodeError(errorMessage) && !this.errorAnalyzer.isDuplicateError(result.errors, errorMessage)) {
        result.errors.push(`Page Error: ${errorMessage}`);
      }
    });

    // 监听请求失败
    page.on('requestfailed', (request) => {
      const networkError = {
        url: request.url(),
        method: request.method(),
        failure: request.failure()?.errorText || 'Unknown error'
      };

      result.networkErrors.push(networkError);

      // 只有关键资源失败才算错误
      if (request.resourceType() === 'document' || request.resourceType() === 'script') {
        result.errors.push(`Network Error: ${request.url()} - ${networkError.failure}`);
      }
    });

    // 注入增强的错误收集脚本
    page.evaluateOnNewDocument(() => {
      window.__pageErrors = [];
      window.__vueErrors = [];
      window.__consoleErrors = [];

      // 捕获全局错误
      window.addEventListener('error', (event) => {
        const errorInfo = {
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          stack: event.error?.stack,
          timestamp: new Date().toISOString(),
          type: 'javascript-error'
        };
        window.__pageErrors.push(errorInfo);
        console.error('Captured global error:', errorInfo);
      });

      // 捕获 Promise 错误
      window.addEventListener('unhandledrejection', (event) => {
        const errorInfo = {
          message: `Unhandled Promise Rejection: ${event.reason}`,
          reason: event.reason,
          stack: event.reason?.stack,
          timestamp: new Date().toISOString(),
          type: 'promise-rejection'
        };
        window.__pageErrors.push(errorInfo);
        console.error('Captured promise rejection:', errorInfo);
      });

      // 拦截 console.error 和 console.warn
      const originalError = console.error;
      const originalWarn = console.warn;

      console.error = function(...args) {
        const message = args.map(arg => {
          if (typeof arg === 'object') {
            try {
              return JSON.stringify(arg);
            } catch {
              return String(arg);
            }
          }
          return String(arg);
        }).join(' ');

        window.__consoleErrors.push({
          level: 'error',
          message: message,
          timestamp: new Date().toISOString(),
          args: args
        });
        originalError.apply(console, args);
      };

      console.warn = function(...args) {
        const message = args.map(arg => {
          if (typeof arg === 'object') {
            try {
              return JSON.stringify(arg);
            } catch {
              return String(arg);
            }
          }
          return String(arg);
        }).join(' ');

        window.__consoleErrors.push({
          level: 'warn',
          message: message,
          timestamp: new Date().toISOString(),
          args: args
        });
        originalWarn.apply(console, args);
      };

      // Vue 错误处理器设置（延迟执行以确保 Vue 已加载）
      function setupVueErrorHandlers() {
        if (typeof window !== 'undefined') {
          // Vue 3 应用错误处理 - 更全面的方法
          if (window.Vue && window.Vue.createApp) {
            const originalCreateApp = window.Vue.createApp;
            window.Vue.createApp = function(...args) {
              const app = originalCreateApp.apply(this, args);

              app.config.errorHandler = (err, instance, info) => {
                const errorInfo = {
                  message: err.message || err.toString(),
                  stack: err.stack,
                  info: info,
                  componentName: instance?.$options?.name || instance?.__name || 'Unknown',
                  timestamp: new Date().toISOString(),
                  type: 'vue3-error'
                };
                window.__vueErrors.push(errorInfo);
                console.error('Vue 3 Error captured:', errorInfo);
              };

              app.config.warnHandler = (msg, instance, trace) => {
                const warnInfo = {
                  message: msg,
                  trace: trace,
                  componentName: instance?.$options?.name || instance?.__name || 'Unknown',
                  timestamp: new Date().toISOString(),
                  type: 'vue3-warning'
                };
                window.__vueErrors.push(warnInfo);
                console.warn('Vue 3 Warning captured:', warnInfo);
              };

              return app;
            };
          }

          // 尝试直接在已存在的Vue应用上设置错误处理器
          if (window.__VUE_DEVTOOLS_GLOBAL_HOOK__ && window.__VUE_DEVTOOLS_GLOBAL_HOOK__.apps) {
            window.__VUE_DEVTOOLS_GLOBAL_HOOK__.apps.forEach(app => {
              if (app.config) {
                app.config.errorHandler = (err, instance, info) => {
                  const errorInfo = {
                    message: err.message || err.toString(),
                    stack: err.stack,
                    info: info,
                    componentName: instance?.$options?.name || instance?.__name || 'Unknown',
                    timestamp: new Date().toISOString(),
                    type: 'vue3-existing-app-error'
                  };
                  window.__vueErrors.push(errorInfo);
                  console.error('Vue 3 Existing App Error captured:', errorInfo);
                };
              }
            });
          }

          // Vue 2 全局错误处理
          if (window.Vue && window.Vue.config) {
            window.Vue.config.errorHandler = function(err, vm, info) {
              window.__vueErrors.push({
                message: err.message || err.toString(),
                stack: err.stack,
                info: info,
                componentName: vm?.$options?.name || 'Unknown',
                timestamp: new Date().toISOString(),
                type: 'vue2-error'
              });
            };

            window.Vue.config.warnHandler = function(msg, vm, trace) {
              window.__vueErrors.push({
                message: msg,
                trace: trace,
                componentName: vm?.$options?.name || 'Unknown',
                timestamp: new Date().toISOString(),
                type: 'vue2-warning'
              });
            };
          }
        }
      }

      // 立即尝试设置
      setupVueErrorHandlers();

      // 延迟设置以确保 Vue 已加载
      setTimeout(setupVueErrorHandlers, 100);
      setTimeout(setupVueErrorHandlers, 500);
      setTimeout(setupVueErrorHandlers, 1000);
      setTimeout(setupVueErrorHandlers, 2000);

      // 主动检测页面错误的函数
      function detectPageErrors() {
        // 检查是否有错误弹框或错误信息
        const errorElements = document.querySelectorAll([
          '[class*="error"]',
          '[class*="Error"]',
          '[id*="error"]',
          '[id*="Error"]',
          '.error-message',
          '.error-dialog',
          '.error-modal',
          '.runtime-error',
          '[data-testid*="error"]'
        ].join(','));

        for (const element of errorElements) {
          const text = element.textContent || element.innerText;
          if (text && text.length > 10 && (
            text.includes('Error') ||
            text.includes('错误') ||
            text.includes('Cannot read properties') ||
            text.includes('TypeError') ||
            text.includes('ReferenceError') ||
            text.includes('Uncaught')
          )) {
            window.__pageErrors.push({
              message: `DOM Error Element: ${text.substring(0, 200)}`,
              element: element.tagName + (element.className ? '.' + element.className : ''),
              timestamp: new Date().toISOString(),
              type: 'dom-error-element'
            });
          }
        }

        // 检查页面标题是否包含错误信息
        if (document.title && (
          document.title.includes('Error') ||
          document.title.includes('错误') ||
          document.title.includes('404') ||
          document.title.includes('500')
        )) {
          window.__pageErrors.push({
            message: `Page Title Error: ${document.title}`,
            timestamp: new Date().toISOString(),
            type: 'page-title-error'
          });
        }
      }

      // 定期检测页面错误
      setTimeout(detectPageErrors, 1000);
      setTimeout(detectPageErrors, 3000);
      setTimeout(detectPageErrors, 5000);
      setTimeout(detectPageErrors, 8000);

      // 监听DOM变化，检测新出现的错误元素
      const observer = new MutationObserver((mutations) => {
        for (const mutation of mutations) {
          if (mutation.type === 'childList') {
            for (const node of mutation.addedNodes) {
              if (node.nodeType === Node.ELEMENT_NODE) {
                const text = node.textContent || node.innerText || '';
                if (text.includes('Uncaught runtime errors') ||
                    text.includes('Cannot read properties of null') ||
                    text.includes('TypeError') ||
                    text.includes('Error')) {
                  window.__pageErrors.push({
                    message: `DOM Mutation Error: ${text.substring(0, 300)}`,
                    timestamp: new Date().toISOString(),
                    type: 'dom-mutation-error'
                  });
                }
              }
            }
          }
        }
      });

      // 确保document.body存在后再观察
      if (document.body) {
        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
      } else {
        // 如果body还没有加载，等待DOM加载完成
        document.addEventListener('DOMContentLoaded', () => {
          if (document.body) {
            observer.observe(document.body, {
              childList: true,
              subtree: true
            });
          }
        });
      }
    });
  }

  /**
   * 生成验证报告
   */
  generateReport() {
    const totalPages = this.validationResults.length;
    const successfulPages = this.validationResults.filter(r => r.success).length;
    const failedPages = this.validationResults.filter(r => !r.success);

    // 收集所有错误进行 FAQ 分析
    const allErrors = [];
    for (const result of this.validationResults) {
      if (result.errors && result.errors.length > 0) {
        allErrors.push(...result.errors);
      }
    }

    // 进行 FAQ 分析
    const faqAnalysis = this.faqHelper.analyzeErrors(allErrors);

    const report = {
      summary: {
        total: totalPages,
        successful: successfulPages,
        failed: failedPages.length,
        successRate: totalPages > 0 ? (successfulPages / totalPages * 100).toFixed(2) : 0
      },
      results: this.validationResults,
      failedPages: failedPages,
      faqAnalysis: faqAnalysis,
      timestamp: new Date().toISOString()
    };

    return report;
  }

  /**
   * 打印验证摘要
   */
  printSummary() {
    const report = this.generateReport();

    console.log(chalk.blue('\n📊 验证结果摘要:'));
    console.log(chalk.gray(`   总页面数: ${report.summary.total}`));
    console.log(chalk.green(`   成功: ${report.summary.successful}`));
    console.log(chalk.red(`   失败: ${report.summary.failed}`));
    console.log(chalk.blue(`   成功率: ${report.summary.successRate}%`));

    if (report.failedPages.length > 0) {
      console.log(chalk.red('\n❌ 失败的页面:'));
      for (const failed of report.failedPages) {
        console.log(chalk.red(`   ${failed.route.path}: ${failed.errors.length} 个错误`));

        // 显示错误的详细信息（改进版）
        if (failed.errors.length > 0) {
          const formattedErrors = this.errorAnalyzer.formatErrorsForDisplay(failed.errors);
          formattedErrors.forEach((errorInfo, index) => {
            console.log(chalk.red(`     ${index + 1}. ${errorInfo.type}: ${errorInfo.summary}`));
            if (errorInfo.details && this.options.verbose) {
              console.log(chalk.gray(`        详情: ${errorInfo.details}`));
            }
          });

          if (failed.errors.length > formattedErrors.length) {
            console.log(chalk.gray(`     ... 还有 ${failed.errors.length - formattedErrors.length} 个类似错误`));
          }
        }
      }

      // 显示 FAQ 分析结果
      if (report.faqAnalysis && report.faqAnalysis.suggestions.length > 0) {
        console.log(chalk.blue('\n💡 常见问题解决建议:'));
        console.log(chalk.gray(report.faqAnalysis.summary));

        // 显示前3个最相关的建议
        const topSuggestions = report.faqAnalysis.suggestions.slice(0, 3);
        for (const suggestion of topSuggestions) {
          const count = report.faqAnalysis.errorCounts.get(suggestion.solution) || 0;
          console.log(chalk.yellow(`   • ${suggestion.solution} (${count} 个相关错误)`));

          if (suggestion.summary && suggestion.summary.cause) {
            console.log(chalk.gray(`     原因: ${suggestion.summary.cause}`));
          }
        }

        console.log(chalk.gray('\n   📖 详细解决方案请参考: docs/runtime-faq-summary.md'));
      }
    }
  }

  /**
   * 清理资源
   */
  async cleanup() {
    try {
      if (this.browser) {
        await this.browser.close();
      }

      if (this.devServer && !this.devServer.killed) {
        this.devServer.kill('SIGTERM');

        // 等待进程结束
        await new Promise((resolve) => {
          this.devServer.on('exit', resolve);
          setTimeout(resolve, 5000); // 5秒超时
        });
      }

      if (this.runtimeErrorHandler) {
        // 清理运行时错误处理器
      }
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️  清理资源时出错: ${error.message}`));
      }
    }
  }

  /**
   * 尝试修复页面错误
   */
  async attemptPageErrorFix(route, errors) {
    try {
      if (!this.buildFixAgent) {
        return { success: false, error: 'BuildFixAgent 未初始化' };
      }

      // 过滤出可以修复的错误（排除登录、权限、网络等非代码错误）
      const fixableErrors = this.errorAnalyzer.filterFixableErrors(errors);

      if (fixableErrors.length === 0) {
        return {
          success: false,
          error: '没有可修复的代码错误',
          filteredCount: errors.length - fixableErrors.length
        };
      }

      // 构建错误上下文信息
      const errorContext = this.buildPageErrorContext(route, fixableErrors);

      if (this.options.verbose) {
        console.log(chalk.gray(`    🔍 分析页面错误: ${route.path}`));
        console.log(chalk.gray(`    总错误数量: ${errors.length}, 可修复错误: ${fixableErrors.length}`));
      }

      // 分析错误并确定需要修复的文件
      const analysisResult = await this.buildFixAgent.errorAnalyzer.analyzeBuildErrors(errorContext.buildOutput, 1);

      if (!analysisResult.success || !analysisResult.filesToFix || analysisResult.filesToFix.length === 0) {
        return {
          success: false,
          error: '无法确定需要修复的文件',
          analysisResult
        };
      }

      if (this.options.verbose) {
        console.log(chalk.gray(`    📁 需要修复的文件: ${analysisResult.filesToFix.join(', ')}`));
      }

      // 执行文件修复
      const fixResult = await this.buildFixAgent.fixFiles(
        analysisResult.filesToFix,
        errorContext.buildOutput,
        1
      );

      return {
        success: fixResult.success,
        filesModified: fixResult.filesModified || 0,
        totalFiles: fixResult.totalFiles || 0,
        errors: fixResult.errors,
        analysisResult,
        fixResult
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        stack: error.stack
      };
    }
  }

  /**
   * 构建页面错误上下文信息
   */
  buildPageErrorContext(route, errors) {
    // 使用路由解析器推断可能的组件文件路径
    let suggestedFiles = [];
    let routeComponentInfo = {};
    if (this.routeParser) {
      const errorMessages = errors.map(error => {
        if (typeof error === 'string') {
          return error;
        } else if (error.message) {
          return `${error.type || 'Error'}: ${error.message}`;
        } else {
          return JSON.stringify(error);
        }
      });

      const errorMessage = errorMessages.join(' ');
      suggestedFiles = this.routeParser.inferComponentPaths(route.path, errorMessage);

      // 获取路由组件映射信息
      const directComponent = this.routeParser.getComponentPathByRoute(route.path);
      if (directComponent) {
        routeComponentInfo.directMapping = directComponent;
      }

      // 获取所有路由映射信息（用于上下文）
      const allMappings = this.routeParser.getRouteComponentMap();
      routeComponentInfo.relatedMappings = this.findRelatedRouteMappings(route.path, allMappings);
    }

    // 构建更详细的路由信息
    const routeInfo = this.buildRouteContextInfo(route);

    // 使用 BuildErrorAnalyzer 统一格式化错误输出
    const errorContext = {
      route,
      errors,
      timestamp: new Date().toISOString(),
      suggestedFiles,
      routeComponentInfo,
      routeInfo
    };

    const buildOutput = this.buildErrorAnalyzer.formatRuntimeErrorAsBuildOutput(errorContext);

    return {
      buildOutput,
      route,
      errors,
      errorCount: errors.length,
      timestamp: new Date().toISOString(),
      suggestedFiles, // 添加推荐的文件列表
      routeComponentInfo, // 添加路由组件信息
      routeInfo // 添加路由上下文信息
    };
  }

  /**
   * 构建路由上下文信息
   */
  buildRouteContextInfo(route) {
    const info = [];

    info.push(`路径: ${route.path}`);
    if (route.name) info.push(`名称: ${route.name}`);
    if (route.component) {
      if (typeof route.component === 'object') {
        info.push(`组件类型: ${route.component.type || 'unknown'}`);
        if (route.component.source) {
          info.push(`组件源: ${route.component.source}`);
        }
      } else {
        info.push(`组件: ${route.component}`);
      }
    }
    if (route.meta && Object.keys(route.meta).length > 0) {
      info.push(`元信息: ${JSON.stringify(route.meta)}`);
    }
    if (route.children && route.children.length > 0) {
      info.push(`子路由数量: ${route.children.length}`);
    }

    return info.join('\n');
  }

  /**
   * 查找相关的路由映射
   */
  findRelatedRouteMappings(currentPath, allMappings) {
    const related = [];
    const pathSegments = currentPath.split('/').filter(Boolean);

    for (const [routePath, component] of allMappings) {
      if (routePath === currentPath) continue;

      const routeSegments = routePath.split('/').filter(Boolean);

      // 查找父路由或同级路由
      if (this.isRelatedRoute(pathSegments, routeSegments)) {
        related.push({ route: routePath, component });
      }
    }

    return related.slice(0, 5); // 限制数量
  }

  /**
   * 判断是否是相关路由
   */
  isRelatedRoute(currentSegments, routeSegments) {
    // 父路由关系
    if (routeSegments.length < currentSegments.length) {
      return routeSegments.every((segment, index) => segment === currentSegments[index]);
    }

    // 同级路由关系
    if (routeSegments.length === currentSegments.length) {
      const commonSegments = routeSegments.filter((segment, index) => segment === currentSegments[index]);
      return commonSegments.length >= Math.max(1, routeSegments.length - 1);
    }

    return false;
  }

  /**
   * 检查页面错误（用于重新验证）
   */
  async checkPageErrors(page) {
    const errors = [];

    try {
      // 检查页面错误数据
      const errorData = await page.evaluate(() => {
        return {
          pageErrors: window.__pageErrors || [],
          vueErrors: window.__vueErrors || [],
          consoleErrors: window.__consoleErrors || []
        };
      });

      // 收集所有错误
      errors.push(...errorData.pageErrors.map(e => ({ type: 'page-error', message: e.message })));
      errors.push(...errorData.vueErrors.map(e => ({ type: 'vue-error', message: e.message })));
      errors.push(...errorData.consoleErrors.filter(e => e.level === 'error').map(e => ({ type: 'console-error', message: e.message })));

      // 检查DOM中的错误元素
      const domErrors = await page.evaluate(() => {
        const errorElements = document.querySelectorAll([
          '[class*="error"]',
          '[class*="Error"]',
          '.error-message',
          '.runtime-error'
        ].join(','));

        const errors = [];
        for (const element of errorElements) {
          const text = element.textContent || element.innerText;
          if (text && text.length > 10 && (
            text.includes('Error') ||
            text.includes('Cannot read properties') ||
            text.includes('TypeError')
          )) {
            errors.push({
              type: 'dom-error',
              message: text.substring(0, 200)
            });
          }
        }
        return errors;
      });

      errors.push(...domErrors);

    } catch (error) {
      errors.push({
        type: 'check-error',
        message: `检查页面错误时出错: ${error.message}`
      });
    }

    return errors;
  }

  /**
   * 工具方法：延迟
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取验证结果
   */
  getResults() {
    return this.validationResults;
  }

  /**
   * 保存报告到文件
   */
  async saveReport(outputPath) {
    const report = this.generateReport();

    // 生成 Markdown 报告
    const markdown = this.generateMarkdownReport(report);

    await fs.writeFile(outputPath, markdown, 'utf8');
    console.log(chalk.green(`📄 验证报告已保存: ${outputPath}`));
  }

  /**
   * 生成 Markdown 格式的报告
   */
  generateMarkdownReport(report) {
    let markdown = `# 页面验证报告\n\n`;
    markdown += `生成时间: ${report.timestamp}\n\n`;

    markdown += `## 摘要\n\n`;
    markdown += `- 总页面数: ${report.summary.total}\n`;
    markdown += `- 成功: ${report.summary.successful}\n`;
    markdown += `- 失败: ${report.summary.failed}\n`;
    markdown += `- 成功率: ${report.summary.successRate}%\n\n`;

    if (report.failedPages.length > 0) {
      markdown += `## 失败的页面\n\n`;
      for (const failed of report.failedPages) {
        markdown += `### ${failed.route.path}\n\n`;
        markdown += `- URL: ${failed.url}\n`;
        markdown += `- 加载时间: ${failed.loadTime}ms\n`;

        if (failed.errors.length > 0) {
          markdown += `- 错误:\n`;
          const formattedErrors = this.errorAnalyzer.formatErrorsForDisplay(failed.errors);
          for (const errorInfo of formattedErrors) {
            markdown += `  - **${errorInfo.type}**: ${errorInfo.summary}\n`;
            if (errorInfo.details) {
              markdown += `    \`\`\`\n    ${errorInfo.details}\n    \`\`\`\n`;
            }
          }

          if (failed.errors.length > formattedErrors.length) {
            markdown += `  - ... 还有 ${failed.errors.length - formattedErrors.length} 个类似错误\n`;
          }
        }

        if (failed.warnings.length > 0) {
          markdown += `- 警告:\n`;
          for (const warning of failed.warnings) {
            markdown += `  - ${warning}\n`;
          }
        }

        markdown += `\n`;
      }
    }

    // 添加 FAQ 分析结果
    if (report.faqAnalysis && report.faqAnalysis.suggestions.length > 0) {
      markdown += `## 💡 常见问题解决建议\n\n`;
      markdown += `${report.faqAnalysis.summary}\n\n`;

      for (const suggestion of report.faqAnalysis.suggestions) {
        const count = report.faqAnalysis.errorCounts.get(suggestion.solution) || 0;
        markdown += `### ${suggestion.solution} (${count} 个相关错误)\n\n`;

        if (suggestion.summary) {
          if (suggestion.summary.cause) {
            markdown += `**根本原因**: ${suggestion.summary.cause}\n\n`;
          }

          if (suggestion.summary.steps.length > 0) {
            markdown += `**解决步骤**:\n`;
            for (const step of suggestion.summary.steps) {
              markdown += `1. ${step}\n`;
            }
            markdown += `\n`;
          }
        }
      }

      markdown += `> 📖 详细解决方案请参考: [docs/runtime-faq-summary.md](docs/runtime-faq-summary.md)\n\n`;
    }

    markdown += `## 详细结果\n\n`;
    for (const result of report.results) {
      const status = result.success ? '✅' : '❌';
      markdown += `- ${status} ${result.route.path} (${result.loadTime}ms)\n`;
    }

    return markdown;
  }
}

module.exports = PageValidator;
