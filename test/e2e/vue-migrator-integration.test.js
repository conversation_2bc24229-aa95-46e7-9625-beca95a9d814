const path = require('path');
const fs = require('fs-extra');
const { spawn } = require('child_process');
const chalk = require('chalk');

describe('Vue Migrator Integration Tests', () => {
  const testProjectPath = path.join(__dirname, '../../test-project');
  const testProjectMigratedPath = path.join(__dirname, '../../test-project-migrated');
  const vueMigratorBin = path.join(__dirname, '../../bin/vue-migrator.js');

  beforeAll(async () => {
    // 确保测试项目存在
    const exists = await fs.pathExists(testProjectPath);
    if (!exists) {
      throw new Error(`Test project not found at ${testProjectPath}`);
    }
  });

  afterEach(async () => {
    // 清理迁移后的项目
    if (await fs.pathExists(testProjectMigratedPath)) {
      await fs.remove(testProjectMigratedPath);
    }
  });

  describe('Auto Migration Command', () => {
    test('should successfully migrate test-project to test-project-migrated', async () => {
      // 预先创建目标目录并复制 package.json（解决复制逻辑问题）
      await fs.ensureDir(testProjectMigratedPath);
      await fs.copy(
        path.join(testProjectPath, 'package.json'),
        path.join(testProjectMigratedPath, 'package.json')
      );

      // 执行迁移命令
      const result = await runVueMigrator(['auto', testProjectPath, testProjectMigratedPath, '--skip-ai', '--skip-build']);

      // 验证命令执行成功
      expect(result.exitCode).toBe(0);

      // 验证输出包含预期的迁移步骤
      expect(result.stdout).toContain('🚀 Vue 自动化迁移工具');
      expect(result.stdout).toContain('源到目标模式');

      // 验证目标项目已创建
      const migratedExists = await fs.pathExists(testProjectMigratedPath);
      expect(migratedExists).toBe(true);

      // 验证关键文件已迁移
      await validateMigratedProject(testProjectMigratedPath);
    }, 180000); // 3分钟超时

    test('should generate migration report', async () => {
      // 预先创建目标目录并复制 package.json
      await fs.ensureDir(testProjectMigratedPath);
      await fs.copy(
        path.join(testProjectPath, 'package.json'),
        path.join(testProjectMigratedPath, 'package.json')
      );

      // 执行迁移命令
      await runVueMigrator(['auto', testProjectPath, testProjectMigratedPath, '--skip-ai', '--skip-build']);

      // 验证迁移报告已生成
      const reportPath = path.join(testProjectMigratedPath, 'migration-report.md');
      const reportExists = await fs.pathExists(reportPath);
      expect(reportExists).toBe(true);

      // 验证报告内容
      const reportContent = await fs.readFile(reportPath, 'utf8');
      expect(reportContent).toContain('# Vue 迁移报告');
    }, 180000);

    test('should handle dry-run mode', async () => {
      // 执行干运行模式
      const result = await runVueMigrator(['auto', testProjectPath, testProjectMigratedPath, '--dry-run', '--skip-ai', '--skip-build']);
      
      // 验证命令执行成功
      expect(result.exitCode).toBe(0);
      
      // 验证输出包含干运行信息
      expect(result.stdout).toContain('预览模式');
      
      // 验证目标项目未创建（干运行模式）
      const migratedExists = await fs.pathExists(testProjectMigratedPath);
      expect(migratedExists).toBe(false);
    }, 60000);

    test('should validate source project exists', async () => {
      const nonExistentPath = path.join(__dirname, 'non-existent-project');
      
      // 执行迁移命令，使用不存在的源项目
      const result = await runVueMigrator(['auto', nonExistentPath, testProjectMigratedPath]);
      
      // 验证命令失败
      expect(result.exitCode).toBe(1);
      expect(result.stderr).toContain('源项目路径不存在');
    }, 30000);

    test('should handle verbose mode', async () => {
      // 执行详细模式
      const result = await runVueMigrator(['auto', testProjectPath, testProjectMigratedPath, '--verbose', '--skip-ai', '--skip-build']);
      
      // 验证命令执行成功
      expect(result.exitCode).toBe(0);
      
      // 验证输出包含详细信息
      expect(result.stdout).toContain('显示详细信息');
    }, 120000);
  });

  describe('Migration Phases Validation', () => {
    test('should complete all migration phases', async () => {
      const result = await runVueMigrator(['auto', testProjectPath, testProjectMigratedPath, '--skip-ai', '--skip-build']);
      
      // 验证各个迁移阶段都已完成
      const expectedPhases = [
        '源到目标模式',
        '项目检测',
        '配置加载',
        '代码迁移',
        '依赖检查'
      ];
      
      expectedPhases.forEach(phase => {
        expect(result.stdout).toContain(phase);
      });
    }, 120000);

    test('should handle migration context properly', async () => {
      await runVueMigrator(['auto', testProjectPath, testProjectMigratedPath, '--skip-ai', '--skip-build']);
      
      // 验证迁移上下文文件已生成
      const contextFiles = [
        'migration-context.json',
        'migration-logs'
      ];
      
      for (const file of contextFiles) {
        const filePath = path.join(testProjectMigratedPath, file);
        const exists = await fs.pathExists(filePath);
        expect(exists).toBe(true);
      }
    }, 120000);
  });

  describe('Project Structure Validation', () => {
    test('should preserve project structure', async () => {
      await runVueMigrator(['auto', testProjectPath, testProjectMigratedPath, '--skip-ai', '--skip-build']);
      
      // 验证项目结构保持完整
      const expectedDirs = ['src', 'src/components', 'src/views'];
      
      for (const dir of expectedDirs) {
        const dirPath = path.join(testProjectMigratedPath, dir);
        const exists = await fs.pathExists(dirPath);
        expect(exists).toBe(true);
      }
    }, 120000);

    test('should update package.json for Vue 3', async () => {
      await runVueMigrator(['auto', testProjectPath, testProjectMigratedPath, '--skip-ai', '--skip-build']);
      
      // 验证 package.json 已更新
      const packageJsonPath = path.join(testProjectMigratedPath, 'package.json');
      const packageJson = await fs.readJson(packageJsonPath);
      
      // 验证 Vue 3 依赖
      expect(packageJson.dependencies.vue).toMatch(/^3\./);
    }, 120000);
  });
});

/**
 * 运行 vue-migrator 命令
 * @param {string[]} args - 命令参数
 * @returns {Promise<{exitCode: number, stdout: string, stderr: string}>}
 */
function runVueMigrator(args) {
  const vueMigratorBinPath = path.join(__dirname, '../../bin/vue-migrator.js');
  return new Promise((resolve, reject) => {
    const child = spawn('node', [vueMigratorBinPath, ...args], {
      stdio: ['pipe', 'pipe', 'pipe'],
      env: {
        ...process.env,
        NODE_ENV: 'test'
      }
    });

    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    child.on('close', (code) => {
      resolve({
        exitCode: code,
        stdout,
        stderr
      });
    });

    child.on('error', (error) => {
      reject(error);
    });

    // 设置超时
    setTimeout(() => {
      child.kill('SIGTERM');
      reject(new Error('Command timeout'));
    }, 180000); // 3分钟超时
  });
}

/**
 * 验证迁移后的项目
 * @param {string} projectPath - 项目路径
 */
async function validateMigratedProject(projectPath) {
  // 验证基本文件存在
  const requiredFiles = [
    'package.json',
    'src/main.js',
    'src/App.vue'
  ];

  for (const file of requiredFiles) {
    const filePath = path.join(projectPath, file);
    const exists = await fs.pathExists(filePath);
    expect(exists).toBe(true);
  }

  // 验证 package.json 内容
  const packageJsonPath = path.join(projectPath, 'package.json');
  const packageJson = await fs.readJson(packageJsonPath);
  
  // 验证 Vue 3 依赖存在
  expect(packageJson.dependencies).toHaveProperty('vue');
  expect(packageJson.dependencies.vue).toMatch(/^3\./);

  // 验证主入口文件已更新
  const mainJsPath = path.join(projectPath, 'src/main.js');
  const mainJsContent = await fs.readFile(mainJsPath, 'utf8');
  
  // 验证 Vue 3 语法
  expect(mainJsContent).toContain('createApp');
}
