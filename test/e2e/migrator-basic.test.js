const path = require('path');
const fs = require('fs-extra');
const { spawn } = require('child_process');

describe('Vue Migrator Basic Tests', () => {
  const testProjectPath = path.join(__dirname, '../../test-project');
  const testProjectMigratedPath = path.join(__dirname, '../../test-project-migrated');
  const vueMigratorBin = path.join(__dirname, '../../bin/vue-migrator.js');

  beforeAll(async () => {
    // 确保测试项目存在
    const exists = await fs.pathExists(testProjectPath);
    if (!exists) {
      throw new Error(`Test project not found at ${testProjectPath}`);
    }
  });

  afterEach(async () => {
    // 清理迁移后的项目
    if (await fs.pathExists(testProjectMigratedPath)) {
      await fs.remove(testProjectMigratedPath);
    }
  });

  test('should execute vue-migrator auto command successfully', async () => {
    console.log('Starting vue-migrator test...');
    console.log(`Source: ${testProjectPath}`);
    console.log(`Target: ${testProjectMigratedPath}`);

    // 预先创建目标目录并复制 package.json（解决复制逻辑问题）
    await fs.ensureDir(testProjectMigratedPath);
    await fs.copy(
      path.join(testProjectPath, 'package.json'),
      path.join(testProjectMigratedPath, 'package.json')
    );

    // 执行迁移命令
    const result = await runCommand('node', [
      vueMigratorBin,
      'auto',
      testProjectPath,
      testProjectMigratedPath,
      '--skip-ai',
      '--skip-build',
      '--verbose'
    ]);

    console.log('Command output:', result.stdout);
    if (result.stderr) {
      console.log('Command errors:', result.stderr);
    }

    // 验证命令执行成功
    expect(result.exitCode).toBe(0);

    // 验证目标项目已创建
    const migratedExists = await fs.pathExists(testProjectMigratedPath);
    expect(migratedExists).toBe(true);

    // 验证基本文件存在
    const packageJsonPath = path.join(testProjectMigratedPath, 'package.json');
    const packageJsonExists = await fs.pathExists(packageJsonPath);
    expect(packageJsonExists).toBe(true);

    // 验证 package.json 内容
    const packageJson = await fs.readJson(packageJsonPath);
    expect(packageJson).toHaveProperty('dependencies');

    console.log('Vue migrator test completed successfully');
  }, 180000); // 3分钟超时

  test('should show help when no arguments provided', async () => {
    const result = await runCommand('node', [vueMigratorBin]);

    // 验证显示帮助信息（帮助信息输出到 stderr）
    const output = result.stdout + result.stderr;
    expect(output).toContain('Vue 2 到 Vue 3 统一迁移工具');
    expect(output).toContain('Usage:');
  }, 30000);

  test('should handle invalid source path', async () => {
    const invalidPath = path.join(__dirname, 'non-existent-project');
    
    const result = await runCommand('node', [
      vueMigratorBin,
      'auto',
      invalidPath,
      testProjectMigratedPath
    ]);
    
    // 验证命令失败并显示错误信息
    expect(result.exitCode).toBe(1);
    expect(result.stderr).toContain('源项目路径不存在');
  }, 30000);
});

/**
 * 运行命令并返回结果
 * @param {string} command - 命令
 * @param {string[]} args - 参数
 * @returns {Promise<{exitCode: number, stdout: string, stderr: string}>}
 */
function runCommand(command, args) {
  return new Promise((resolve, reject) => {
    console.log(`Running: ${command} ${args.join(' ')}`);
    
    const child = spawn(command, args, {
      stdio: ['pipe', 'pipe', 'pipe'],
      env: {
        ...process.env,
        NODE_ENV: 'test'
      }
    });

    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data) => {
      const text = data.toString();
      stdout += text;
      // 实时输出，便于调试
      process.stdout.write(`[STDOUT] ${text}`);
    });

    child.stderr.on('data', (data) => {
      const text = data.toString();
      stderr += text;
      // 实时输出，便于调试
      process.stderr.write(`[STDERR] ${text}`);
    });

    child.on('close', (code) => {
      console.log(`Command finished with exit code: ${code}`);
      resolve({
        exitCode: code,
        stdout,
        stderr
      });
    });

    child.on('error', (error) => {
      console.error('Command error:', error);
      reject(error);
    });

    // 设置超时
    const timeout = setTimeout(() => {
      console.log('Command timeout, killing process...');
      child.kill('SIGTERM');
      reject(new Error('Command timeout'));
    }, 200000); // 200秒超时

    child.on('close', () => {
      clearTimeout(timeout);
    });
  });
}
